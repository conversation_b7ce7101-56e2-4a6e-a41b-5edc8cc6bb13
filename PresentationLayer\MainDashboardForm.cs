using DevExpress.XtraEditors;
using DevExpress.XtraTileControl;
using DXApplication1.Utilities;
using DXApplication1.Models;

namespace DXApplication1.PresentationLayer
{
    /// <summary>
    /// نموذج لوحة التحكم الرئيسية - Main Dashboard Form
    /// </summary>
    public partial class MainDashboardForm : XtraForm
    {
        private User? _currentUser;

        public MainDashboardForm(User currentUser)
        {
            InitializeComponent();
            _currentUser = currentUser;
            SetupForm();
            SetupTiles();
            LoadDashboardData();
        }

        private void SetupForm()
        {
            // إعداد النموذج للغة العربية - Setup form for Arabic
            this.Text = $"نظام إدارة المبيعات - مرحباً {_currentUser?.FullName}";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private void SetupTiles()
        {
            // إعداد البلاطات حسب صلاحيات المستخدم - Setup tiles based on user permissions
            if (_currentUser?.Role == null) return;

            // بلاطة العملاء - Customers Tile
            if (_currentUser.Role.CanManageCustomers)
            {
                var customersTile = CreateTile("العملاء", "إدارة بيانات العملاء", TileItemSize.Medium);
                customersTile.ItemClick += CustomersTile_ItemClick;
            }

            // بلاطة المنتجات - Products Tile
            if (_currentUser.Role.CanManageProducts)
            {
                var productsTile = CreateTile("المنتجات", "إدارة المنتجات والمخزون", TileItemSize.Medium);
                productsTile.ItemClick += ProductsTile_ItemClick;
            }

            // بلاطة الفواتير - Invoices Tile
            if (_currentUser.Role.CanCreateInvoices)
            {
                var invoicesTile = CreateTile("الفواتير", "إنشاء وإدارة الفواتير", TileItemSize.Medium);
                invoicesTile.ItemClick += InvoicesTile_ItemClick;
            }

            // بلاطة التقارير - Reports Tile
            if (_currentUser.Role.CanViewReports)
            {
                var reportsTile = CreateTile("التقارير", "عرض التقارير والإحصائيات", TileItemSize.Medium);
                reportsTile.ItemClick += ReportsTile_ItemClick;
            }

            // بلاطة المستخدمين - Users Tile
            if (_currentUser.Role.CanManageUsers)
            {
                var usersTile = CreateTile("المستخدمين", "إدارة المستخدمين والصلاحيات", TileItemSize.Medium);
                usersTile.ItemClick += UsersTile_ItemClick;
            }

            // بلاطة الإعدادات - Settings Tile
            if (_currentUser.Role.CanManageSettings)
            {
                var settingsTile = CreateTile("الإعدادات", "إعدادات النظام", TileItemSize.Small);
                settingsTile.ItemClick += SettingsTile_ItemClick;
            }
        }

        private TileItem CreateTile(string text, string description, TileItemSize size)
        {
            var tile = new TileItem
            {
                Text = text,
                Size = size,
                ItemSize = size
            };

            // إضافة وصف للبلاطة - Add description to tile
            var element = new TileItemElement
            {
                Text = description,
                TextAlignment = TileItemContentAlignment.BottomLeft
            };
            tile.Elements.Add(element);

            tileControl1.Groups[0].Items.Add(tile);
            return tile;
        }

        private void LoadDashboardData()
        {
            try
            {
                // تحميل بيانات لوحة التحكم - Load dashboard data
                LoadSummaryData();
                LoadRecentActivities();
                LoadChartData();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSummaryData()
        {
            // TODO: Load summary statistics
            // عدد العملاء، المنتجات، الفواتير، إجمالي المبيعات
        }

        private void LoadRecentActivities()
        {
            // TODO: Load recent activities
            // آخر الفواتير، آخر العملاء المضافين، إلخ
        }

        private void LoadChartData()
        {
            // TODO: Load chart data
            // مخططات المبيعات، أداء المنتجات، إلخ
        }

        // معالجات الأحداث - Event Handlers
        private void CustomersTile_ItemClick(object sender, TileItemEventArgs e)
        {
            // فتح نموذج إدارة العملاء - Open customers management form
            var customersForm = new CustomersForm();
            customersForm.ShowDialog();
        }

        private void ProductsTile_ItemClick(object sender, TileItemEventArgs e)
        {
            // فتح نموذج إدارة المنتجات - Open products management form
            var productsForm = new ProductsForm();
            productsForm.ShowDialog();
        }

        private void InvoicesTile_ItemClick(object sender, TileItemEventArgs e)
        {
            // فتح نموذج إدارة الفواتير - Open invoices management form
            var invoicesForm = new InvoicesForm();
            invoicesForm.ShowDialog();
        }

        private void ReportsTile_ItemClick(object sender, TileItemEventArgs e)
        {
            // فتح نموذج التقارير - Open reports form
            var reportsForm = new ReportsForm();
            reportsForm.ShowDialog();
        }

        private void UsersTile_ItemClick(object sender, TileItemEventArgs e)
        {
            // فتح نموذج إدارة المستخدمين - Open users management form
            var usersForm = new UsersForm();
            usersForm.ShowDialog();
        }

        private void SettingsTile_ItemClick(object sender, TileItemEventArgs e)
        {
            // فتح نموذج الإعدادات - Open settings form
            var settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
        }

        private void MainDashboardForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = XtraMessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد الإغلاق", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result != DialogResult.Yes)
            {
                e.Cancel = true;
            }
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            var result = XtraMessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDashboardData();
        }
    }

    // نماذج وهمية للاختبار - Dummy forms for testing
    public class CustomersForm : XtraForm
    {
        public CustomersForm()
        {
            this.Text = "إدارة العملاء";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class ProductsForm : XtraForm
    {
        public ProductsForm()
        {
            this.Text = "إدارة المنتجات";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class InvoicesForm : XtraForm
    {
        public InvoicesForm()
        {
            this.Text = "إدارة الفواتير";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class ReportsForm : XtraForm
    {
        public ReportsForm()
        {
            this.Text = "التقارير";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class UsersForm : XtraForm
    {
        public UsersForm()
        {
            this.Text = "إدارة المستخدمين";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }

    public class SettingsForm : XtraForm
    {
        public SettingsForm()
        {
            this.Text = "الإعدادات";
            this.RightToLeft = RightToLeft.Yes;
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterParent;
        }
    }
}
