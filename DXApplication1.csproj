<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- DevExpress Controls -->
    <PackageReference Include="DevExpress.Win.Design" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.Grid" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.Charts" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.Navigation" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.Editors" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.TreeList" Version="24.2.8" />
    <PackageReference Include="DevExpress.Reporting.Core" Version="24.2.8" />
    <PackageReference Include="DevExpress.Win.Printing" Version="24.2.8" />

    <!-- Database and Entity Framework -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />

    <!-- Security -->
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />

    <!-- Configuration -->
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />

    <!-- Logging -->
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />

    <!-- PDF Export -->
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
  </ItemGroup>

</Project>
