using DevExpress.XtraEditors;
using DXApplication1.Utilities;

namespace DXApplication1.PresentationLayer
{
    /// <summary>
    /// نموذج تسجيل الدخول - Login Form
    /// </summary>
    public partial class LoginForm : XtraForm
    {
        public LoginForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            // إعداد النموذج للغة العربية - Setup form for Arabic
            this.Text = "تسجيل الدخول - نظام إدارة المبيعات";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Size = new Size(400, 300);
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من البيانات المدخلة - Validate input data
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    XtraMessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    XtraMessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                // محاولة تسجيل الدخول - Attempt login
                // For demo purposes, create a sample user
                var sampleUser = new Models.User
                {
                    Id = 1,
                    Username = txtUsername.Text,
                    FullName = "مدير النظام",
                    Role = new Models.Role
                    {
                        Id = 1,
                        RoleName = "مدير النظام",
                        CanManageUsers = true,
                        CanManageCustomers = true,
                        CanManageProducts = true,
                        CanCreateInvoices = true,
                        CanViewReports = true,
                        CanManageSettings = true
                    }
                };

                // فتح لوحة التحكم الرئيسية - Open main dashboard
                this.Hide();
                var dashboardForm = new MainDashboardForm(sampleUser);
                var result = dashboardForm.ShowDialog();

                if (result == DialogResult.OK)
                {
                    // المستخدم سجل الخروج - User logged out
                    this.Show();
                    txtPassword.Text = "";
                    txtUsername.Focus();
                }
                else
                {
                    // إغلاق التطبيق - Close application
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في تسجيل الدخول:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
